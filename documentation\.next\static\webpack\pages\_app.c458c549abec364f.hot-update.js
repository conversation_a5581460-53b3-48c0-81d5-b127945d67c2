"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/components/Layout.jsx":
/*!***********************************!*\
  !*** ./src/components/Layout.jsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"Layout\": function() { return /* binding */ Layout; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"./node_modules/clsx/dist/clsx.m.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Hero */ \"./src/components/Hero.jsx\");\n/* harmony import */ var _components_MobileNavigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MobileNavigation */ \"./src/components/MobileNavigation.jsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Navigation */ \"./src/components/Navigation.jsx\");\n/* harmony import */ var _components_Prose__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Prose */ \"./src/components/Prose.jsx\");\n/* harmony import */ var _components_ThemeSelector__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ThemeSelector */ \"./src/components/ThemeSelector.jsx\");\n\n\n\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar navigation = [\n    {\n        title: \"Panel\",\n        links: [\n            {\n                title: \"Getting started\",\n                href: \"/\"\n            },\n            {\n                title: \"Installation\",\n                href: \"/docs/installation\"\n            },\n            {\n                title: \"Terminology\",\n                href: \"/docs/terminology\"\n            },\n            {\n                title: \"Development\",\n                href: \"/docs/development\"\n            },\n            {\n                title: \"Plugins\",\n                href: \"/docs/plugins\"\n            },\n            {\n                title: \"API reference\",\n                href: \"/docs/api-reference\"\n            },\n            {\n                title: \"How do you make an omelette?\",\n                href: \"/docs/omelette-guide\"\n            }, \n        ]\n    },\n    {\n        title: \"Daemon (airlinkd)\",\n        links: [\n            {\n                title: \"Installation\",\n                href: \"/docs/daemon\"\n            }\n        ]\n    }\n];\nfunction GitHubIcon(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({\n        \"aria-hidden\": \"true\",\n        viewBox: \"0 0 16 16\"\n    }, props), {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M8 0C3.58 0 0 3.58 0 8C0 11.54 2.29 14.53 5.47 15.59C5.87 15.66 6.02 15.42 6.02 15.21C6.02 15.02 6.01 14.39 6.01 13.72C4 14.09 3.48 13.23 3.32 12.78C3.23 12.55 2.84 11.84 2.5 11.65C2.22 11.5 1.82 11.13 2.49 11.12C3.12 11.11 3.57 11.7 3.72 11.94C4.44 13.15 5.59 12.81 6.05 12.6C6.12 12.08 6.33 11.73 6.56 11.53C4.78 11.33 2.92 10.64 2.92 7.58C2.92 6.71 3.23 5.99 3.74 5.43C3.66 5.23 3.38 4.41 3.82 3.31C3.82 3.31 4.49 3.1 6.02 4.13C6.66 3.95 7.34 3.86 8.02 3.86C8.7 3.86 9.38 3.95 10.02 4.13C11.55 3.09 12.22 3.31 12.22 3.31C12.66 4.41 12.38 5.23 12.3 5.43C12.81 5.99 13.12 6.7 13.12 7.58C13.12 10.65 11.25 11.33 9.47 11.53C9.76 11.78 10.01 12.26 10.01 13.01C10.01 14.08 10 14.94 10 15.21C10 15.42 10.15 15.67 10.55 15.59C13.71 14.53 16 11.53 16 8C16 3.58 12.42 0 8 0Z\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }), void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c = GitHubIcon;\nfunction Header(param) {\n    var navigation = param.navigation;\n    _s();\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), isScrolled = ref[0], setIsScrolled = ref[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function() {\n        var onScroll = function onScroll() {\n            setIsScrolled(window.scrollY > 0);\n        };\n        onScroll();\n        window.addEventListener(\"scroll\", onScroll, {\n            passive: true\n        });\n        return function() {\n            window.removeEventListener(\"scroll\", onScroll, {\n                passive: true\n            });\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"sticky top-0 z-50 flex flex-wrap items-center justify-between bg-white px-4 py-5 shadow-md shadow-neutral-900/5 transition duration-500 dark:shadow-none sm:px-6 lg:px-8\", isScrolled ? \"dark:bg-neutral-900/95 dark:backdrop-blur dark:[@supports(backdrop-filter:blur(0))]:bg-neutral-900/75\" : \"dark:bg-transparent\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-6 flex lg:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MobileNavigation__WEBPACK_IMPORTED_MODULE_6__.MobileNavigation, {\n                    navigation: navigation\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex flex-grow basis-0 items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    \"aria-label\": \"Home page\",\n                    className: \"text-xl font-semibold text-neutral-800 dark:text-white\",\n                    children: \"Airlink\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex basis-0 justify-end gap-6 sm:gap-8 md:flex-grow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeSelector__WEBPACK_IMPORTED_MODULE_9__.ThemeSelector, {\n                        className: \"relative z-10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"https://github.com/airlinklabs\",\n                        className: \"group\",\n                        \"aria-label\": \"GitHub\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GitHubIcon, {\n                            className: \"h-6 w-6 fill-neutral-400 group-hover:fill-neutral-500 dark:group-hover:fill-neutral-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"R6oiTacYga5DaYN6hPJc8sHhe7Y=\");\n_c1 = Header;\nfunction useTableOfContents(tableOfContents) {\n    var ref;\n    _s1();\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)((ref = tableOfContents[0]) === null || ref === void 0 ? void 0 : ref.id), currentSection = ref1[0], setCurrentSection = ref1[1];\n    var getHeadings = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function(tableOfContents) {\n        return tableOfContents.flatMap(function(node) {\n            return [\n                node.id\n            ].concat((0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(node.children.map(function(child) {\n                return child.id;\n            })));\n        }).map(function(id) {\n            var el = document.getElementById(id);\n            if (!el) return;\n            var style = window.getComputedStyle(el);\n            var scrollMt = parseFloat(style.scrollMarginTop);\n            var top = window.scrollY + el.getBoundingClientRect().top - scrollMt;\n            return {\n                id: id,\n                top: top\n            };\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function() {\n        var onScroll = function onScroll() {\n            var top = window.scrollY;\n            var current = headings[0].id;\n            var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n            try {\n                for(var _iterator = headings[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                    var heading = _step.value;\n                    if (top >= heading.top) {\n                        current = heading.id;\n                    } else {\n                        break;\n                    }\n                }\n            } catch (err) {\n                _didIteratorError = true;\n                _iteratorError = err;\n            } finally{\n                try {\n                    if (!_iteratorNormalCompletion && _iterator.return != null) {\n                        _iterator.return();\n                    }\n                } finally{\n                    if (_didIteratorError) {\n                        throw _iteratorError;\n                    }\n                }\n            }\n            setCurrentSection(current);\n        };\n        if (tableOfContents.length === 0) return;\n        var headings = getHeadings(tableOfContents);\n        window.addEventListener(\"scroll\", onScroll, {\n            passive: true\n        });\n        onScroll();\n        return function() {\n            window.removeEventListener(\"scroll\", onScroll, {\n                passive: true\n            });\n        };\n    }, [\n        getHeadings,\n        tableOfContents\n    ]);\n    return currentSection;\n}\n_s1(useTableOfContents, \"KI5oKQ6fFe5w4/Wu+OlEJ2/pQyM=\");\nfunction Layout(param) {\n    var children = param.children, title = param.title, tableOfContents = param.tableOfContents;\n    var _this = this;\n    _s2();\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    var isHomePage = router.pathname === \"/\";\n    var allLinks = navigation.flatMap(function(section) {\n        return section.links;\n    });\n    var linkIndex = allLinks.findIndex(function(link) {\n        return link.href === router.pathname;\n    });\n    var previousPage = allLinks[linkIndex - 1];\n    var nextPage = allLinks[linkIndex + 1];\n    var section = navigation.find(function(section) {\n        return section.links.find(function(link) {\n            return link.href === router.pathname;\n        });\n    });\n    var currentSection = useTableOfContents(tableOfContents);\n    function isActive(section) {\n        if (section.id === currentSection) {\n            return true;\n        }\n        if (!section.children) {\n            return false;\n        }\n        return section.children.findIndex(isActive) > -1;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                navigation: navigation\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            isHomePage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_5__.Hero, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                lineNumber: 151,\n                columnNumber: 22\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative mx-auto flex max-w-8xl justify-center sm:px-2 lg:px-8 xl:px-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:relative lg:block lg:flex-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-y-0 right-0 w-[50vw] bg-neutral-50 dark:hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-[4.5rem] -ml-0.5 h-[calc(100vh-4.5rem)] overflow-y-auto py-16 pl-0.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-16 bottom-0 right-0 hidden h-12 w-px bg-gradient-to-t from-neutral-800 dark:block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-28 bottom-0 right-0 hidden w-px bg-neutral-800 dark:block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_7__.Navigation, {\n                                        navigation: navigation,\n                                        className: \"w-64 pr-8 xl:w-72 xl:pr-16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-0 max-w-2xl flex-auto px-4 py-16 lg:max-w-none lg:pr-0 lg:pl-8 xl:px-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                children: [\n                                    (title || section) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                        className: \"mb-9 space-y-1\",\n                                        children: [\n                                            section && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-display text-sm font-medium text-neutral-300\",\n                                                children: section.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this),\n                                            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"font-display text-3xl tracking-tight text-neutral-900 dark:text-white\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Prose__WEBPACK_IMPORTED_MODULE_8__.Prose, {\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                className: \"mt-12 flex border-t border-neutral-200 pt-6 dark:border-neutral-800\",\n                                children: [\n                                    previousPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"font-display text-sm font-medium text-neutral-900 dark:text-white\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: previousPage.href,\n                                                    className: \"text-base font-semibold text-neutral-500 hover:text-neutral-600 dark:text-neutral-400 dark:hover:text-neutral-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            \"aria-hidden\": \"true\",\n                                                            children: \"←\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" \",\n                                                        previousPage.title\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    nextPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-auto text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                className: \"font-display text-sm font-medium text-neutral-900 dark:text-white\",\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                className: \"mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: nextPage.href,\n                                                    className: \"text-base font-semibold text-neutral-500 hover:text-neutral-600 dark:text-neutral-400 dark:hover:text-neutral-300\",\n                                                    children: [\n                                                        nextPage.title,\n                                                        \" \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            \"aria-hidden\": \"true\",\n                                                            children: \"→\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 38\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden xl:sticky xl:top-[4.5rem] xl:-mr-6 xl:block xl:h-[calc(100vh-4.5rem)] xl:flex-none xl:overflow-y-auto xl:py-16 xl:pr-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            \"aria-labelledby\": \"on-this-page-title\",\n                            className: \"w-56\",\n                            children: tableOfContents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        id: \"on-this-page-title\",\n                                        className: \"font-display text-sm font-medium text-neutral-900 dark:text-white\",\n                                        children: \"On this page\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        role: \"list\",\n                                        className: \"mt-4 space-y-3 text-sm\",\n                                        children: tableOfContents.map(function(section) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"#\".concat(section.id),\n                                                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(isActive(section) ? \"text-neutral-500\" : \"font-normal text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300\"),\n                                                            children: section.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 25\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, _this),\n                                                    section.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                        role: \"list\",\n                                                        className: \"mt-2 space-y-3 pl-5 text-neutral-500 dark:text-neutral-400\",\n                                                        children: section.children.map(function(subSection) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                    href: \"#\".concat(subSection.id),\n                                                                    className: isActive(subSection) ? \"text-neutral-500\" : \"hover:text-neutral-600 dark:hover:text-neutral-300\",\n                                                                    children: subSection.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 31\n                                                                }, _this)\n                                                            }, subSection.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 29\n                                                            }, _this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 25\n                                                    }, _this)\n                                                ]\n                                            }, section.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, _this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\airlink\\\\documentation\\\\src\\\\components\\\\Layout.jsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(Layout, \"WXmmQk56bQvS6+83s2e2Ty4urSE=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        useTableOfContents\n    ];\n});\n_c2 = Layout;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"GitHubIcon\");\n$RefreshReg$(_c1, \"Header\");\n$RefreshReg$(_c2, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.jsx\n"));

/***/ })

});