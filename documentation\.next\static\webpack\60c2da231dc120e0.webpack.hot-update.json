{"c": ["webpack"], "r": ["pages/docs/development", "pages/docs/plugins", "pages/docs/api-reference"], "m": ["./markdoc/nodes.js", "./markdoc/tags.js", "./node_modules/@markdoc/markdoc/dist/index.mjs", "./node_modules/@markdoc/next.js/src/runtime.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CNoah%5CDocuments%5Cairlink%5Cdocumentation%5Csrc%5Cpages%5Cdocs%5Cdevelopment.md&page=%2Fdocs%2Fdevelopment!", "./src/components/Callout.jsx", "./src/components/Fence.jsx", "./src/components/Icon.jsx", "./src/components/QuickLinks.jsx", "./src/components/icons/InstallationIcon.jsx", "./src/components/icons/LightbulbIcon.jsx", "./src/components/icons/PluginsIcon.jsx", "./src/components/icons/PresetsIcon.jsx", "./src/components/icons/ThemingIcon.jsx", "./src/components/icons/WarningIcon.jsx", "./src/pages/docs/development.md", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CNoah%5CDocuments%5Cairlink%5Cdocumentation%5Csrc%5Cpages%5Cdocs%5Cplugins.md&page=%2Fdocs%2Fplugins!", "./src/pages/docs/plugins.md", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CNoah%5CDocuments%5Cairlink%5Cdocumentation%5Csrc%5Cpages%5Cdocs%5Capi-reference.md&page=%2Fdocs%2Fapi-reference!", "./src/pages/docs/api-reference.md"]}