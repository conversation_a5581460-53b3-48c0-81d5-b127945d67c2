"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/docs/installation",{

/***/ "./src/pages/docs/installation.md":
/*!****************************************!*\
  !*** ./src/pages/docs/installation.md ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"__N_SSG\": function() { return /* binding */ __N_SSG; },\n/* harmony export */   \"default\": function() { return /* binding */ MarkdocComponent; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _markdoc_markdoc__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @markdoc/markdoc */ \"./node_modules/@markdoc/markdoc/dist/index.mjs\");\n/* harmony import */ var C_Users_Noah_Documents_airlink_documentation_node_modules_markdoc_next_js_src_runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@markdoc/next.js/src/runtime.js */ \"./node_modules/@markdoc/next.js/src/runtime.js\");\n/* harmony import */ var C_Users_Noah_Documents_airlink_documentation_markdoc_tags_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./markdoc/tags.js */ \"./markdoc/tags.js\");\n/* harmony import */ var C_Users_Noah_Documents_airlink_documentation_markdoc_nodes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./markdoc/nodes.js */ \"./markdoc/nodes.js\");\n\n\n// renderers is imported separately so Markdoc isn't sent to the client\n\n\n/**\n * Schema is imported like this so end-user's code is compiled using build-in babel/webpack configs.\n * This enables typescript/ESnext support\n */ var config = {};\n\n\nvar functions = {};\nvar schema = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n    tags: (0,C_Users_Noah_Documents_airlink_documentation_node_modules_markdoc_next_js_src_runtime_js__WEBPACK_IMPORTED_MODULE_1__.defaultObject)(C_Users_Noah_Documents_airlink_documentation_markdoc_tags_js__WEBPACK_IMPORTED_MODULE_2__),\n    nodes: (0,C_Users_Noah_Documents_airlink_documentation_node_modules_markdoc_next_js_src_runtime_js__WEBPACK_IMPORTED_MODULE_1__.defaultObject)(C_Users_Noah_Documents_airlink_documentation_markdoc_nodes_js__WEBPACK_IMPORTED_MODULE_3__),\n    functions: (0,C_Users_Noah_Documents_airlink_documentation_node_modules_markdoc_next_js_src_runtime_js__WEBPACK_IMPORTED_MODULE_1__.defaultObject)(functions)\n}, (0,C_Users_Noah_Documents_airlink_documentation_node_modules_markdoc_next_js_src_runtime_js__WEBPACK_IMPORTED_MODULE_1__.defaultObject)(config));\nvar components = (0,C_Users_Noah_Documents_airlink_documentation_node_modules_markdoc_next_js_src_runtime_js__WEBPACK_IMPORTED_MODULE_1__.getSchema)(schema).components;\nvar __N_SSG = true;\nfunction MarkdocComponent(props) {\n    // Only execute HMR code in development\n    return _markdoc_markdoc__WEBPACK_IMPORTED_MODULE_5__.renderers.react(props.markdoc.content, (react__WEBPACK_IMPORTED_MODULE_0___default()), {\n        components: (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({}, components, props.components)\n    });\n}\n_c = MarkdocComponent;\nvar _c;\n$RefreshReg$(_c, \"MarkdocComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/docs/installation.md\n"));

/***/ })

});